<?php

namespace Tests\Unit\Services\Externals;

use App\Services\Externals\Contracts\ExportServiceInterface;
use App\Services\Externals\ExportService;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\ConnectException;
use GuzzleHttp\Exception\RequestException;
use GuzzleHttp\Promise\Promise;
use GuzzleHttp\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Psr7\Utils;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Mockery;
use Psr\Http\Message\StreamInterface;
use Symfony\Component\HttpFoundation\StreamedResponse;
use Tests\TestCase;

/**
 * Test class for ExportService
 *
 * Tests the export service functionality including streaming exports,
 * file downloads, and error handling with proper dependency mocking
 * for Laravel Octane compatibility.
 *
 * @covers \App\Services\Externals\ExportService
 */
class ExportServiceTest extends TestCase
{
    private ExportService $exportService;
    private Client $mockClient;
    private array $testData;
    private array $supportedFormats;

    protected function setUp(): void
    {
        parent::setUp();

        $this->mockClient = Mockery::mock(Client::class);
        $this->supportedFormats = ['csv', 'xlsx', 'pdf', 'json'];
        $this->testData = [
            ['id' => 1, 'name' => 'Test 1', 'email' => '<EMAIL>'],
            ['id' => 2, 'name' => 'Test 2', 'email' => '<EMAIL>'],
        ];

        $this->exportService = new ExportService(
            client: $this->mockClient,
            maxRetries: 3,
            supportedFormats: $this->supportedFormats,
            exportServiceUrl: 'http://test-export-service.com'
        );
    }

    protected function tearDown(): void
    {
        Mockery::close();
        parent::tearDown();
    }

    /**
     * Test service implements interface
     */
    public function test_implements_export_service_interface(): void
    {
        $this->assertInstanceOf(ExportServiceInterface::class, $this->exportService);
    }

    /**
     * Test successful stream export
     */
    public function test_stream_export_success(): void
    {
        $exportType = 'csv';
        $expectedResult = ['download_url' => 'http://test.com/download/123'];

        $mockStream = Mockery::mock(StreamInterface::class);
        $mockStream->shouldReceive('__toString')
            ->once()
            ->andReturn(json_encode($expectedResult));

        $mockResponse = Mockery::mock(Response::class);
        $mockResponse->shouldReceive('getBody')
            ->once()
            ->andReturn($mockStream);

        $this->mockClient->shouldReceive('request')
            ->once()
            ->with(
                'POST',
                'http://test-export-service.com/export',
                Mockery::on(function ($options) use ($exportType) {
                    return isset($options['headers']['X-Export-Type']) &&
                           $options['headers']['X-Export-Type'] === $exportType &&
                           isset($options['headers']['Content-Type']) &&
                           $options['headers']['Content-Type'] === 'application/x-ndjson';
                })
            )
            ->andReturn($mockResponse);

        Log::shouldReceive('info')
            ->twice(); // Once for start, once for completion

        $result = $this->exportService->streamExport($this->testData, $exportType);

        $this->assertEquals($expectedResult, $result);
    }

    /**
     * Test stream export with invalid type
     */
    public function test_stream_export_invalid_type(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid export type: invalid');

        $this->exportService->streamExport($this->testData, 'invalid');
    }

    /**
     * Test stream export with Guzzle exception
     */
    public function test_stream_export_guzzle_exception(): void
    {
        $exportType = 'csv';
        $exception = new RequestException(
            'Service unavailable',
            new Request('POST', 'test'),
            new Response(503)
        );

        $this->mockClient->shouldReceive('request')
            ->once()
            ->andThrow($exception);

        Log::shouldReceive('info')->once();
        Log::shouldReceive('error')->once();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Failed to export data: Service unavailable');

        $this->exportService->streamExport($this->testData, $exportType);
    }

    /**
     * Test async export success
     */
    public function test_async_export_success(): void
    {
        $exportType = 'xlsx';
        $expectedResult = ['job_id' => '123', 'status' => 'processing'];

        $mockPromise = Mockery::mock(Promise::class);
        $mockPromise->shouldReceive('then')
            ->once()
            ->with(Mockery::type('callable'), Mockery::type('callable'))
            ->andReturnSelf();

        $this->mockClient->shouldReceive('sendAsync')
            ->once()
            ->with(Mockery::on(function ($request) use ($exportType) {
                return $request->getMethod() === 'POST' &&
                       $request->getHeaderLine('X-Export-Type') === $exportType &&
                       $request->getHeaderLine('X-Async') === 'true';
            }))
            ->andReturn($mockPromise);

        Log::shouldReceive('info')->once();

        $result = $this->exportService->asyncExport($this->testData, $exportType);

        $this->assertSame($mockPromise, $result);
    }

    /**
     * Test get default filename
     */
    public function test_get_default_filename(): void
    {
        $exportType = 'csv';
        $filename = $this->exportService->getDefaultFilename($exportType);

        $this->assertStringStartsWith('export_', $filename);
        $this->assertStringEndsWith('.csv', $filename);
        $this->assertMatchesRegularExpression('/export_\d{4}-\d{2}-\d{2}_\d{2}-\d{2}-\d{2}\.csv/', $filename);
    }

    /**
     * Test get default filename with invalid type
     */
    public function test_get_default_filename_invalid_type(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid export type: invalid');

        $this->exportService->getDefaultFilename('invalid');
    }

    /**
     * Test get content type for export
     */
    public function test_get_content_type_for_export(): void
    {
        $expectedTypes = [
            'csv' => 'text/csv',
            'xlsx' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'pdf' => 'application/pdf',
            'json' => 'application/json',
        ];

        foreach ($expectedTypes as $type => $expectedContentType) {
            $contentType = $this->exportService->getContentTypeForExport($type);
            $this->assertEquals($expectedContentType, $contentType);
        }
    }

    /**
     * Test get content type with invalid type
     */
    public function test_get_content_type_invalid_type(): void
    {
        $this->expectException(\InvalidArgumentException::class);
        $this->expectExceptionMessage('Invalid export type: invalid');

        $this->exportService->getContentTypeForExport('invalid');
    }

    /**
     * Test check export progress success
     */
    public function test_check_export_progress_success(): void
    {
        $jobId = 'test-job-123';
        $expectedResult = ['status' => 'completed', 'progress' => 100];

        $mockStream = Mockery::mock(StreamInterface::class);
        $mockStream->shouldReceive('__toString')
            ->once()
            ->andReturn(json_encode($expectedResult));

        $mockResponse = Mockery::mock(Response::class);
        $mockResponse->shouldReceive('getBody')
            ->once()
            ->andReturn($mockStream);

        $this->mockClient->shouldReceive('request')
            ->once()
            ->with('GET', "http://test-export-service.com/export/{$jobId}/status")
            ->andReturn($mockResponse);

        $result = $this->exportService->checkExportProgress($jobId);

        $this->assertEquals($expectedResult, $result);
    }

    /**
     * Test check export progress with exception
     */
    public function test_check_export_progress_exception(): void
    {
        $jobId = 'test-job-123';
        $exception = new RequestException(
            'Not found',
            new Request('GET', 'test'),
            new Response(404)
        );

        $this->mockClient->shouldReceive('request')
            ->once()
            ->andThrow($exception);

        Log::shouldReceive('error')->once();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Failed to check export progress: Not found');

        $this->exportService->checkExportProgress($jobId);
    }

    /**
     * Test paginated export initialization failure
     */
    public function test_paginated_export_no_export_id(): void
    {
        $dataProvider = function ($offset, $limit) {
            return array_slice($this->testData, $offset, $limit);
        };

        $mockResponse = Mockery::mock(Response::class);
        $mockResponse->shouldReceive('getBody')
            ->once()
            ->andReturn(json_encode([])); // No export_id

        $this->mockClient->shouldReceive('request')
            ->once()
            ->andReturn($mockResponse);

        Log::shouldReceive('info')->once();
        Log::shouldReceive('error')->once();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Failed to initialize paginated export: No export ID returned');

        $this->exportService->paginatedExport($dataProvider, 2, 1, 'csv');
    }

    /**
     * Test download exported file success
     */
    public function test_download_exported_file_success(): void
    {
        $downloadUrl = 'http://test.com/download/123';
        $filename = 'test_export.csv';
        $contentType = 'text/csv';

        Http::fake([
            $downloadUrl => Http::response('test,data\n1,value', 200)
        ]);

        Log::shouldReceive('info')->once();

        $response = $this->exportService->downloadExportedFile($downloadUrl, $filename, $contentType);

        $this->assertInstanceOf(StreamedResponse::class, $response);
        $this->assertEquals(200, $response->getStatusCode());
        $this->assertEquals($contentType, $response->headers->get('Content-Type'));
        $this->assertEquals('attachment; filename="' . $filename . '"', $response->headers->get('Content-Disposition'));
    }

    /**
     * Test download exported file with HTTP failure
     */
    public function test_download_exported_file_http_failure(): void
    {
        $downloadUrl = 'http://test.com/download/123';
        $filename = 'test_export.csv';
        $contentType = 'text/csv';

        Http::fake([
            $downloadUrl => Http::response('Not Found', 404)
        ]);

        Log::shouldReceive('info')->once();
        Log::shouldReceive('error')->once();

        $this->expectException(\Exception::class);
        $this->expectExceptionMessage('Failed to download exported file');

        $this->exportService->downloadExportedFile($downloadUrl, $filename, $contentType);
    }

    /**
     * Test paginated export success
     */
    public function test_paginated_export_success(): void
    {
        $dataProvider = function ($offset, $limit) {
            return array_slice($this->testData, $offset, $limit);
        };

        $exportId = 'test-export-123';
        $expectedResult = ['download_url' => 'http://test.com/download/456'];

        // Mock init response
        $initResponse = Mockery::mock(Response::class);
        $initResponse->shouldReceive('getBody')
            ->once()
            ->andReturn(json_encode(['export_id' => $exportId]));

        // Mock finalize response
        $finalizeResponse = Mockery::mock(Response::class);
        $finalizeResponse->shouldReceive('getBody')
            ->once()
            ->andReturn(json_encode($expectedResult));

        $this->mockClient->shouldReceive('request')
            ->with('POST', 'http://test-export-service.com/paginated-export/init', Mockery::any())
            ->once()
            ->andReturn($initResponse);

        // Mock chunk requests (2 chunks for 2 items with chunk size 1)
        $this->mockClient->shouldReceive('request')
            ->with('POST', 'http://test-export-service.com/paginated-export/chunk', Mockery::any())
            ->twice();

        $this->mockClient->shouldReceive('request')
            ->with('POST', 'http://test-export-service.com/paginated-export/finalize', Mockery::any())
            ->once()
            ->andReturn($finalizeResponse);

        Log::shouldReceive('info')->atLeast()->once();

        $result = $this->exportService->paginatedExport($dataProvider, 2, 1, 'csv');

        $this->assertEquals($expectedResult, $result);
    }

    /**
     * Test service is stateless for Octane compatibility
     */
    public function test_service_is_stateless(): void
    {
        // Create two instances with same parameters
        $service1 = new ExportService(
            client: $this->mockClient,
            maxRetries: 3,
            supportedFormats: $this->supportedFormats,
            exportServiceUrl: 'http://test-export-service.com'
        );

        $service2 = new ExportService(
            client: $this->mockClient,
            maxRetries: 3,
            supportedFormats: $this->supportedFormats,
            exportServiceUrl: 'http://test-export-service.com'
        );

        // Both should behave identically
        $this->assertEquals($service1->getDefaultFilename('csv'), $service2->getDefaultFilename('csv'));
        $this->assertEquals($service1->getContentTypeForExport('csv'), $service2->getContentTypeForExport('csv'));
    }

    /**
     * Test constructor with default parameters
     */
    public function test_constructor_with_defaults(): void
    {
        $service = new ExportService();

        $this->assertInstanceOf(ExportService::class, $service);
        $this->assertEquals('text/csv', $service->getContentTypeForExport('csv'));
    }

    /**
     * Test all supported formats have content types
     */
    public function test_all_supported_formats_have_content_types(): void
    {
        foreach ($this->supportedFormats as $format) {
            $contentType = $this->exportService->getContentTypeForExport($format);
            $this->assertNotEmpty($contentType);
            $this->assertStringContainsString('/', $contentType); // Should be a valid MIME type
        }
    }
}

<?php

namespace App\Http\Controllers;

use App\Services\Externals\ExportService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\StreamedResponse;

class ExportController extends Controller
{
    /**
     * @var ExportService
     */
    protected ExportService $exportService;

    /**
     * Constructor with service injection
     *
     * @param ExportService $exportService
     */
    public function __construct(ExportService $exportService)
    {
        $this->exportService = $exportService;
    }

    /**
     * Download a file from the export service
     *
     * @param Request $request
     * @return StreamedResponse
     */
    public function downloadExportedFile(Request $request)
    {
        try {
            // Validate request
            $request->validate([
                'url' => 'required|string',
                'filename' => 'nullable|string',
                'type' => 'nullable|string|in:csv,xlsx,pdf,json',
            ]);

            $downloadUrl = $request->input('url');
            $exportType = $request->input('type', 'csv');

            // Get filename from request or generate default
            $filename = $request->input('filename') ??
                $this->exportService->getDefaultFilename($exportType);

            // Get content type for export
            $contentType = $this->exportService->getContentTypeForExport($exportType);

            Log::info('Starting export file download', [
                'url' => $downloadUrl,
                'filename' => $filename,
                'type' => $exportType
            ]);

            // Return streamed download response
            return $this->exportService->downloadExportedFile($downloadUrl, $filename, $contentType);
        } catch (\Exception $e) {
            Log::error('Export download failed', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            abort(500, 'Failed to download file: ' . $e->getMessage());
        }
    }

    /**
     * Process an export and then download the result
     *
     * @param Request $request
     * @return StreamedResponse
     */
    public function processAndDownload(Request $request)
    {
        try {
            // Validate the request data as needed
            $request->validate([
                'data' => 'required|array',
                'type' => 'required|string|in:csv,xlsx,pdf,json',
                'filename' => 'nullable|string',
            ]);

            $exportType = $request->input('type', 'csv');
            $data = $request->input('data');
            $filename = $request->input('filename') ??
                $this->exportService->getDefaultFilename($exportType);

            // Stream the data to export service
            $exportResult = $this->exportService->streamExport($data, $exportType);

            if (!isset($exportResult['download_url'])) {
                throw new \Exception('Export service did not return a download URL');
            }

            // Log successful export
            Log::info('Export processed successfully', [
                'type' => $exportType,
                'rows' => count($data),
                'download_url' => $exportResult['download_url']
            ]);

            // Get content type for export
            $contentType = $this->exportService->getContentTypeForExport($exportType);

            // Return streamed download response
            return $this->exportService->downloadExportedFile(
                $exportResult['download_url'],
                $filename,
                $contentType
            );
        } catch (\Exception $e) {
            Log::error('Export processing failed', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            abort(500, 'Failed to process export: ' . $e->getMessage());
        }
    }
}

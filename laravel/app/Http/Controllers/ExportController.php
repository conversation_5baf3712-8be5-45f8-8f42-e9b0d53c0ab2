<?php

namespace App\Http\Controllers;

use App\Services\Externals\Contracts\ExportServiceInterface;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\StreamedResponse;

/**
 * Export Controller
 *
 * Handles HTTP requests for export operations, delegating business logic
 * to the ExportService. Follows Laravel controller best practices with
 * minimal business logic and proper dependency injection.
 */
class ExportController extends Controller
{
    /**
     * @var ExportServiceInterface
     */
    protected ExportServiceInterface $exportService;

    /**
     * Constructor with service injection
     *
     * @param ExportServiceInterface $exportService
     */
    public function __construct(ExportServiceInterface $exportService)
    {
        $this->exportService = $exportService;
    }

    /**
     * Download a file from the export service
     *
     * @param Request $request
     * @return StreamedResponse
     */
    public function downloadExportedFile(Request $request): StreamedResponse
    {
        try {
            // Validate request
            $validated = $request->validate([
                'url' => 'required|string|url',
                'filename' => 'nullable|string|max:255',
                'type' => 'nullable|string|in:csv,xlsx,pdf,json',
            ]);

            $downloadUrl = $validated['url'];
            $exportType = $validated['type'] ?? 'csv';

            // Get filename from request or generate default
            $filename = $validated['filename'] ??
                $this->exportService->getDefaultFilename($exportType);

            // Get content type for export
            $contentType = $this->exportService->getContentTypeForExport($exportType);

            Log::info('Starting export file download', [
                'url' => $downloadUrl,
                'filename' => $filename,
                'type' => $exportType,
                'user_id' => auth()->id(),
                'context' => 'EXPORT_DOWNLOAD'
            ]);

            // Return streamed download response
            return $this->exportService->downloadExportedFile($downloadUrl, $filename, $contentType);
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::warning('Export download validation failed', [
                'errors' => $e->errors(),
                'user_id' => auth()->id(),
                'context' => 'VALIDATION_ERROR'
            ]);
            throw $e;
        } catch (\InvalidArgumentException $e) {
            Log::warning('Export download invalid argument', [
                'message' => $e->getMessage(),
                'user_id' => auth()->id(),
                'context' => 'INVALID_ARGUMENT'
            ]);
            abort(400, 'Invalid export parameters: ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Export download failed', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id(),
                'context' => 'EXPORT_ERROR'
            ]);

            abort(500, 'Failed to download file: ' . $e->getMessage());
        }
    }

    /**
     * Process an export and then download the result
     *
     * @param Request $request
     * @return StreamedResponse
     */
    public function processAndDownload(Request $request): StreamedResponse
    {
        try {
            // Validate the request data
            $validated = $request->validate([
                'data' => 'required|array|min:1',
                'type' => 'required|string|in:csv,xlsx,pdf,json',
                'filename' => 'nullable|string|max:255',
                'options' => 'nullable|array',
            ]);

            $exportType = $validated['type'];
            $data = $validated['data'];
            $options = $validated['options'] ?? [];
            $filename = $validated['filename'] ??
                $this->exportService->getDefaultFilename($exportType);

            Log::info('Starting export processing', [
                'type' => $exportType,
                'rows' => count($data),
                'filename' => $filename,
                'user_id' => auth()->id(),
                'context' => 'EXPORT_PROCESSING'
            ]);

            // Stream the data to export service
            $exportResult = $this->exportService->streamExport($data, $exportType, $options);

            if (!isset($exportResult['download_url'])) {
                throw new \Exception('Export service did not return a download URL');
            }

            // Log successful export
            Log::info('Export processed successfully', [
                'type' => $exportType,
                'rows' => count($data),
                'download_url' => $exportResult['download_url'],
                'user_id' => auth()->id(),
                'context' => 'EXPORT_SUCCESS'
            ]);

            // Get content type for export
            $contentType = $this->exportService->getContentTypeForExport($exportType);

            // Return streamed download response
            return $this->exportService->downloadExportedFile(
                $exportResult['download_url'],
                $filename,
                $contentType
            );
        } catch (\Illuminate\Validation\ValidationException $e) {
            Log::warning('Export processing validation failed', [
                'errors' => $e->errors(),
                'user_id' => auth()->id(),
                'context' => 'VALIDATION_ERROR'
            ]);
            throw $e;
        } catch (\InvalidArgumentException $e) {
            Log::warning('Export processing invalid argument', [
                'message' => $e->getMessage(),
                'user_id' => auth()->id(),
                'context' => 'INVALID_ARGUMENT'
            ]);
            abort(400, 'Invalid export parameters: ' . $e->getMessage());
        } catch (\Exception $e) {
            Log::error('Export processing failed', [
                'message' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'user_id' => auth()->id(),
                'context' => 'EXPORT_ERROR'
            ]);

            abort(500, 'Failed to process export: ' . $e->getMessage());
        }
    }
}

<?php

namespace App\Services\Externals;

use Guzzle<PERSON>ttp\Client;
use GuzzleHttp\Exception\GuzzleException;
use GuzzleHttp\HandlerStack;
use GuzzleHttp\Middleware;
use GuzzleHttp\Promise\PromiseInterface;
use Guz<PERSON>Http\Psr7\Request;
use GuzzleHttp\Psr7\Response;
use GuzzleHttp\Psr7\Utils;
use Illuminate\Support\Facades\Log;
use Psr\Http\Message\ResponseInterface;

class ExportService
{
    /** @var Client */
    private Client $client;

    /** @var int */
    private int $maxRetries;

    /** @var array */
    private array $supportedFormats = ['csv', 'xlsx', 'pdf', 'json'];

    /**
     * ExportService constructor with dependency injection
     *
     * @param Client|null $client
     * @param int $maxRetries
     */
    public function __construct(Client $client = null, int $maxRetries = 3)
    {
        $this->maxRetries = $maxRetries;

        if ($client) {
            $this->client = $client;
        } else {
            $stack = HandlerStack::create();
            // Add retry middleware
            $stack->push($this->retryMiddleware());

            $this->client = new Client([
                'handler' => $stack,
                'timeout' => 60,
                'connect_timeout' => 10,
            ]);
        }
    }

    /**
     * Stream data for export
     *
     * @param iterable $dataRows
     * @param string $exportType
     * @param array $options
     * @return array
     * @throws \Exception
     */
    public function streamExport(iterable $dataRows, string $exportType = 'csv', array $options = []): array
    {
        $this->validateExportType($exportType);

        try {
            Log::info('Starting export process', ['type' => $exportType, 'options' => $options]);

            $stream = Utils::streamFor((function () use ($dataRows) {
                foreach ($dataRows as $row) {
                    yield json_encode($row) . "\n";
                }
            })());

            $headers = [
                'Content-Type' => 'application/x-ndjson',
                'X-Export-Type' => $exportType,
            ];

            // Add any custom headers from options
            if (isset($options['headers']) && is_array($options['headers'])) {
                $headers = array_merge($headers, $options['headers']);
            }

            $requestOptions = [
                'headers' => $headers,
                'body' => $stream,
            ];

            // Add any additional request options
            if (isset($options['request_options']) && is_array($options['request_options'])) {
                $requestOptions = array_merge($requestOptions, $options['request_options']);
            }

            $response = $this->client->request(
                'POST',
                config('services.export_service.url') . '/export',
                $requestOptions
            );

            $result = json_decode($response->getBody(), true);

            Log::info('Export completed successfully', [
                'type' => $exportType,
                'download_url' => $result['download_url'] ?? null
            ]);

            return $result;
        } catch (GuzzleException $e) {
            Log::error('Export service error', [
                'message' => $e->getMessage(),
                'code' => $e->getCode(),
                'type' => $exportType
            ]);

            throw new \Exception('Failed to export data: ' . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * Asynchronous export for large datasets
     *
     * @param iterable $dataRows
     * @param string $exportType
     * @param array $options
     * @return PromiseInterface
     */
    public function asyncExport(iterable $dataRows, string $exportType = 'csv', array $options = []): PromiseInterface
    {
        $this->validateExportType($exportType);

        Log::info('Starting async export process', ['type' => $exportType]);

        $stream = Utils::streamFor((function () use ($dataRows) {
            foreach ($dataRows as $row) {
                yield json_encode($row) . "\n";
            }
        })());

        $headers = [
            'Content-Type' => 'application/x-ndjson',
            'X-Export-Type' => $exportType,
            'X-Async' => 'true'
        ];

        // Add any custom headers from options
        if (isset($options['headers']) && is_array($options['headers'])) {
            $headers = array_merge($headers, $options['headers']);
        }

        $request = new Request(
            'POST',
            config('services.export_service.url') . '/export',
            $headers,
            $stream
        );

        return $this->client->sendAsync($request)
            ->then(
                function (ResponseInterface $response) use ($exportType) {
                    Log::info('Async export completed', ['type' => $exportType]);
                    return json_decode($response->getBody(), true);
                },
                function (\Exception $e) use ($exportType) {
                    Log::error('Async export failed', [
                        'type' => $exportType,
                        'message' => $e->getMessage()
                    ]);
                    throw $e;
                }
            );
    }

    /**
     * Export with pagination for very large datasets
     *
     * @param callable $dataProvider Function that accepts offset and limit and returns a chunk of data
     * @param int $totalCount Total number of records
     * @param int $chunkSize Size of each chunk
     * @param string $exportType Type of export
     * @param array $options Additional options
     * @return array
     * @throws \Exception
     */
    public function paginatedExport(
        callable $dataProvider,
        int $totalCount,
        int $chunkSize = 5000,
        string $exportType = 'csv',
        array $options = []
    ): array {
        $this->validateExportType($exportType);

        try {
            Log::info('Starting paginated export', [
                'type' => $exportType,
                'total' => $totalCount,
                'chunk_size' => $chunkSize
            ]);

            $headers = [
                'Content-Type' => 'application/x-ndjson',
                'X-Export-Type' => $exportType,
                'X-Paginated' => 'true',
                'X-Total-Count' => $totalCount
            ];

            // Add any custom headers from options
            if (isset($options['headers']) && is_array($options['headers'])) {
                $headers = array_merge($headers, $options['headers']);
            }

            $response = $this->client->request(
                'POST',
                config('services.export_service.url') . '/paginated-export/init',
                ['headers' => $headers]
            );

            $initResult = json_decode($response->getBody(), true);
            $exportId = $initResult['export_id'] ?? null;

            if (!$exportId) {
                throw new \Exception('Failed to initialize paginated export: No export ID returned');
            }

            // Process chunks
            for ($offset = 0; $offset < $totalCount; $offset += $chunkSize) {
                $dataChunk = $dataProvider($offset, $chunkSize);

                $stream = Utils::streamFor((function () use ($dataChunk) {
                    foreach ($dataChunk as $row) {
                        yield json_encode($row) . "\n";
                    }
                })());

                $chunkHeaders = [
                    'Content-Type' => 'application/x-ndjson',
                    'X-Export-ID' => $exportId,
                    'X-Chunk-Offset' => $offset,
                    'X-Chunk-Size' => $chunkSize
                ];

                $this->client->request(
                    'POST',
                    config('services.export_service.url') . '/paginated-export/chunk',
                    [
                        'headers' => $chunkHeaders,
                        'body' => $stream
                    ]
                );

                Log::info('Exported chunk', ['offset' => $offset, 'size' => $chunkSize]);
            }

            // Finalize export
            $response = $this->client->request(
                'POST',
                config('services.export_service.url') . '/paginated-export/finalize',
                ['headers' => ['X-Export-ID' => $exportId]]
            );

            $result = json_decode($response->getBody(), true);

            Log::info('Paginated export completed', [
                'type' => $exportType,
                'download_url' => $result['download_url'] ?? null
            ]);

            return $result;
        } catch (GuzzleException $e) {
            Log::error('Paginated export failed', [
                'message' => $e->getMessage(),
                'code' => $e->getCode()
            ]);

            throw new \Exception('Failed to complete paginated export: ' . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * Check export progress using job ID
     *
     * @param string $jobId
     * @return array
     * @throws \Exception
     */
    public function checkExportProgress(string $jobId): array
    {
        try {
            $response = $this->client->request(
                'GET',
                config('services.export_service.url') . "/export/{$jobId}/status"
            );

            return json_decode($response->getBody(), true);
        } catch (GuzzleException $e) {
            Log::error('Failed to check export progress', [
                'job_id' => $jobId,
                'message' => $e->getMessage()
            ]);

            throw new \Exception('Failed to check export progress: ' . $e->getMessage(), $e->getCode(), $e);
        }
    }

    /**
     * Create retry middleware for Guzzle
     *
     * @return callable
     */
    private function retryMiddleware(): callable
    {
        return Middleware::retry(
            function (
                $retries,
                Request $request,
                Response $response = null,
                \Exception $exception = null
            ) {
                // Don't retry if we've reached the max retries
                if ($retries >= $this->maxRetries) {
                    return false;
                }

                // Retry on connection exceptions
                if ($exception instanceof \GuzzleHttp\Exception\ConnectException) {
                    Log::warning('Retrying export request after connection error', [
                        'attempt' => $retries + 1,
                        'max_retries' => $this->maxRetries,
                        'message' => $exception->getMessage()
                    ]);
                    return true;
                }

                // Retry on 5xx server errors
                if ($response && $response->getStatusCode() >= 500) {
                    Log::warning('Retrying export request after server error', [
                        'attempt' => $retries + 1,
                        'max_retries' => $this->maxRetries,
                        'status_code' => $response->getStatusCode()
                    ]);
                    return true;
                }

                return false;
            },
            function ($retries) {
                // Exponential backoff with jitter: 2^retries * 100ms + random milliseconds
                return (2 ** $retries) * 1000 + random_int(0, 1000);
            }
        );
    }

    /**
     * Validate the export type
     *
     * @param string $exportType
     * @throws \InvalidArgumentException
     */
    private function validateExportType(string $exportType): void
    {
        if (!in_array($exportType, $this->supportedFormats)) {
            throw new \InvalidArgumentException(
                "Invalid export type: {$exportType}. Supported types: " . implode(', ', $this->supportedFormats)
            );
        }
    }
}
